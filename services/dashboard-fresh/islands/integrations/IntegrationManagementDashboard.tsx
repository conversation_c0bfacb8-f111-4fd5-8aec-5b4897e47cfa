import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { Integration, IntegrationFilters, IntegrationSortOptions, PLATFORM_TEMPLATES } from "../../types/integrations.ts";
import IntegrationGrid from "../../components/integrations/IntegrationGrid.tsx";
import IntegrationStatsCards from "../../components/integrations/IntegrationStatsCards.tsx";
import CreateIntegrationModal from "./CreateIntegrationModal.tsx";
import IntegrationFiltersBar from "../../components/integrations/IntegrationFiltersBar.tsx";

interface IntegrationManagementDashboardProps {
  className?: string;
}

export default function IntegrationManagementDashboard({ className = "" }: IntegrationManagementDashboardProps) {
  // Signals for state management
  const integrations = useSignal<Integration[]>([]);
  const loading = useSignal(true);
  const error = useSignal<string | null>(null);
  const showCreateModal = useSignal(false);
  const selectedIntegration = useSignal<Integration | null>(null);
  const totalCount = useSignal(0);
  const currentPage = useSignal(0);
  const pageSize = useSignal(20);
  
  // Filter and sort state
  const filters = useSignal<IntegrationFilters>({});
  const sortOptions = useSignal<IntegrationSortOptions>({
    sort_by: "created_at",
    sort_order: "desc"
  });

  // Computed values
  const hasMore = useComputed(() => {
    return (currentPage.value + 1) * pageSize.value < totalCount.value;
  });

  const connectedIntegrations = useComputed(() => {
    return integrations.value.filter(integration => integration.is_active && integration.status === "active");
  });

  const totalSyncedRecords = useComputed(() => {
    return integrations.value.reduce((sum, integration) => 
      sum + (integration.sync_stats?.total_records_synced || 0), 0
    );
  });

  const averageSyncTime = useComputed(() => {
    const activeIntegrations = integrations.value.filter(integration => 
      integration.is_active && integration.sync_stats?.avg_sync_duration
    );
    
    if (activeIntegrations.length === 0) return 0;
    
    const totalTime = activeIntegrations.reduce((sum, integration) => 
      sum + (integration.sync_stats?.avg_sync_duration || 0), 0
    );
    
    return Math.round(totalTime / activeIntegrations.length);
  });

  // Load integrations function
  const loadIntegrations = async (reset = false) => {
    try {
      loading.value = true;
      error.value = null;

      const params = new URLSearchParams({
        limit: pageSize.value.toString(),
        offset: reset ? "0" : (currentPage.value * pageSize.value).toString(),
        sort_by: sortOptions.value.sort_by,
        sort_order: sortOptions.value.sort_order,
      });

      // Add filters
      if (filters.value.platform) params.set("platform", filters.value.platform);
      if (filters.value.status) params.set("status", filters.value.status);
      if (filters.value.is_active !== undefined) params.set("is_active", filters.value.is_active.toString());
      if (filters.value.search) params.set("search", filters.value.search);

      const response = await fetch(`/api/integrations/integrations?${params.toString()}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        if (reset) {
          integrations.value = data.data;
          currentPage.value = 0;
        } else {
          integrations.value = [...integrations.value, ...data.data];
        }
        totalCount.value = data.pagination?.total || data.data.length;
      } else {
        throw new Error(data.error || "Failed to load integrations");
      }
    } catch (err) {
      console.error("Error loading integrations:", err);
      error.value = err instanceof Error ? err.message : "Failed to load integrations";
      
      // Fallback to show available platforms when service is unavailable
      if (reset && integrations.value.length === 0) {
        integrations.value = [];
      }
    } finally {
      loading.value = false;
    }
  };

  // Load more integrations (pagination)
  const loadMore = () => {
    if (!loading.value && hasMore.value) {
      currentPage.value += 1;
      loadIntegrations(false);
    }
  };

  // Refresh integrations
  const refreshIntegrations = () => {
    loadIntegrations(true);
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: IntegrationFilters) => {
    filters.value = newFilters;
    loadIntegrations(true);
  };

  // Handle sort changes
  const handleSortChange = (newSort: IntegrationSortOptions) => {
    sortOptions.value = newSort;
    loadIntegrations(true);
  };

  // Handle integration creation
  const handleIntegrationCreated = (newIntegration: Integration) => {
    integrations.value = [newIntegration, ...integrations.value];
    totalCount.value += 1;
    showCreateModal.value = false;
  };

  // Handle integration update
  const handleIntegrationUpdated = (updatedIntegration: Integration) => {
    integrations.value = integrations.value.map(integration => 
      integration.id === updatedIntegration.id ? updatedIntegration : integration
    );
  };

  // Handle integration deletion
  const handleIntegrationDeleted = (integrationId: string) => {
    integrations.value = integrations.value.filter(integration => integration.id !== integrationId);
    totalCount.value -= 1;
  };

  // Handle sync start
  const handleSyncStarted = (integrationId: string) => {
    integrations.value = integrations.value.map(integration => 
      integration.id === integrationId 
        ? { ...integration, status: "active" as const }
        : integration
    );
  };

  // Load initial data
  useEffect(() => {
    loadIntegrations(true);
  }, []);

  return (
    <div class={`integration-management-dashboard ${className}`}>
      {/* Stats Cards */}
      <IntegrationStatsCards
        totalIntegrations={integrations.value.length}
        connectedIntegrations={connectedIntegrations.value.length}
        totalSyncedRecords={totalSyncedRecords.value}
        averageSyncTime={averageSyncTime.value}
        loading={loading.value}
      />

      {/* Filters and Actions Bar */}
      <div class="mb-6 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <IntegrationFiltersBar
          filters={filters.value}
          onFiltersChange={handleFiltersChange}
          onRefresh={refreshIntegrations}
          loading={loading.value}
        />
        
        <button
          type="button"
          onClick={() => showCreateModal.value = true}
          class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors font-medium flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Add Integration
        </button>
      </div>

      {/* Error Message */}
      {error.value && (
        <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-red-800 dark:text-red-200">{error.value}</span>
          </div>
        </div>
      )}

      {/* Integrations Grid */}
      <IntegrationGrid
        integrations={integrations.value}
        availablePlatforms={Object.values(PLATFORM_TEMPLATES)}
        loading={loading.value}
        sortOptions={sortOptions.value}
        onSortChange={handleSortChange}
        onIntegrationUpdate={handleIntegrationUpdated}
        onIntegrationDelete={handleIntegrationDeleted}
        onSyncStart={handleSyncStarted}
        onLoadMore={hasMore.value ? loadMore : undefined}
        hasMore={hasMore.value}
        onCreateIntegration={() => showCreateModal.value = true}
      />

      {/* Create Integration Modal */}
      {showCreateModal.value && (
        <CreateIntegrationModal
          isOpen={showCreateModal.value}
          onClose={() => showCreateModal.value = false}
          onIntegrationCreated={handleIntegrationCreated}
        />
      )}
    </div>
  );
}
