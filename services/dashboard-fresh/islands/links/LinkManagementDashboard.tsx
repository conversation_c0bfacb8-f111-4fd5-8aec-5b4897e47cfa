import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { Link, LinkFilters, LinkSortOptions } from "../../types/links.ts";
import LinkTable from "../../components/links/LinkTable.tsx";
import LinkStatsCards from "../../components/links/LinkStatsCards.tsx";
import CreateLinkModal from "./CreateLinkModal.tsx";
import LinkFiltersBar from "../../components/links/LinkFiltersBar.tsx";

interface LinkManagementDashboardProps {
  className?: string;
}

export default function LinkManagementDashboard({ className = "" }: LinkManagementDashboardProps) {
  // Signals for state management
  const links = useSignal<Link[]>([]);
  const loading = useSignal(true);
  const error = useSignal<string | null>(null);
  const showCreateModal = useSignal(false);
  const selectedLink = useSignal<Link | null>(null);
  const totalCount = useSignal(0);
  const currentPage = useSignal(0);
  const pageSize = useSignal(20);
  
  // Filter and sort state
  const filters = useSignal<LinkFilters>({});
  const sortOptions = useSignal<LinkSortOptions>({
    sort_by: "created_at",
    sort_order: "desc"
  });

  // Computed values
  const hasMore = useComputed(() => {
    return (currentPage.value + 1) * pageSize.value < totalCount.value;
  });

  const totalClicks = useComputed(() => {
    return links.value.reduce((sum, link) => sum + (link.total_clicks || 0), 0);
  });

  const activeLinksCount = useComputed(() => {
    return links.value.filter(link => link.is_active).length;
  });

  const averageClickRate = useComputed(() => {
    const activeLinks = links.value.filter(link => link.is_active);
    if (activeLinks.length === 0) return 0;
    
    const totalClicks = activeLinks.reduce((sum, link) => sum + (link.total_clicks || 0), 0);
    return totalClicks / activeLinks.length;
  });

  // Load links function
  const loadLinks = async (reset = false) => {
    try {
      loading.value = true;
      error.value = null;

      const params = new URLSearchParams({
        limit: pageSize.value.toString(),
        offset: reset ? "0" : (currentPage.value * pageSize.value).toString(),
        sort_by: sortOptions.value.sort_by,
        sort_order: sortOptions.value.sort_order,
      });

      // Add filters
      if (filters.value.search) params.set("search", filters.value.search);
      if (filters.value.is_active !== undefined) params.set("is_active", filters.value.is_active.toString());
      if (filters.value.utm_source) params.set("utm_source", filters.value.utm_source);
      if (filters.value.utm_medium) params.set("utm_medium", filters.value.utm_medium);

      const response = await fetch(`/api/links/list?${params.toString()}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        if (reset) {
          links.value = data.data;
          currentPage.value = 0;
        } else {
          links.value = [...links.value, ...data.data];
        }
        totalCount.value = data.pagination.total;
      } else {
        throw new Error(data.error || "Failed to load links");
      }
    } catch (err) {
      console.error("Error loading links:", err);
      error.value = err instanceof Error ? err.message : "Failed to load links";
    } finally {
      loading.value = false;
    }
  };

  // Load more links (pagination)
  const loadMore = () => {
    if (!loading.value && hasMore.value) {
      currentPage.value += 1;
      loadLinks(false);
    }
  };

  // Refresh links
  const refreshLinks = () => {
    loadLinks(true);
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: LinkFilters) => {
    filters.value = newFilters;
    loadLinks(true);
  };

  // Handle sort changes
  const handleSortChange = (newSort: LinkSortOptions) => {
    sortOptions.value = newSort;
    loadLinks(true);
  };

  // Handle link creation
  const handleLinkCreated = (newLink: Link) => {
    links.value = [newLink, ...links.value];
    totalCount.value += 1;
    showCreateModal.value = false;
  };

  // Handle link update
  const handleLinkUpdated = (updatedLink: Link) => {
    links.value = links.value.map(link => 
      link.id === updatedLink.id ? updatedLink : link
    );
  };

  // Handle link deletion
  const handleLinkDeleted = (linkId: string) => {
    links.value = links.value.filter(link => link.id !== linkId);
    totalCount.value -= 1;
  };

  // Load initial data
  useEffect(() => {
    loadLinks(true);
  }, []);

  return (
    <div class={`link-management-dashboard ${className}`}>
      {/* Stats Cards */}
      <LinkStatsCards
        totalLinks={links.value.length}
        totalClicks={totalClicks.value}
        activeLinks={activeLinksCount.value}
        averageClickRate={averageClickRate.value}
        loading={loading.value}
      />

      {/* Filters and Actions Bar */}
      <div class="mb-6 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <LinkFiltersBar
          filters={filters.value}
          onFiltersChange={handleFiltersChange}
          onRefresh={refreshLinks}
          loading={loading.value}
        />
        
        <button
          type="button"
          onClick={() => showCreateModal.value = true}
          class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors font-medium flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Create Link
        </button>
      </div>

      {/* Error Message */}
      {error.value && (
        <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-red-800 dark:text-red-200">{error.value}</span>
          </div>
        </div>
      )}

      {/* Links Table */}
      <LinkTable
        links={links.value}
        loading={loading.value}
        sortOptions={sortOptions.value}
        onSortChange={handleSortChange}
        onLinkUpdate={handleLinkUpdated}
        onLinkDelete={handleLinkDeleted}
        onLoadMore={hasMore.value ? loadMore : undefined}
        hasMore={hasMore.value}
      />

      {/* Create Link Modal */}
      {showCreateModal.value && (
        <CreateLinkModal
          isOpen={showCreateModal.value}
          onClose={() => showCreateModal.value = false}
          onLinkCreated={handleLinkCreated}
        />
      )}
    </div>
  );
}
